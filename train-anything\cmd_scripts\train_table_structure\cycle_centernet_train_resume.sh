#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.:src

# 初始化accelerate配置，以及安装必要依赖
pip install omegaconf>=2.3.0 tqdm Pillow opencv-python
export ACC_CFG=tsr_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space

# 数据路径设置
TRAIN_DATA_DIR=${TRANSPACE_DIR}/datasets/table_structure/train
VAL_DATA_DIR=${TRANSPACE_DIR}/datasets/table_structure/val
OUTPUT_DIR=${TRANSPACE_DIR}/training_outputs/cycle_centernet/v$(date +%Y%m%d%H%M)_resume
CHECKPOINT_PATH=${TRANSPACE_DIR}/training_outputs/cycle_centernet/v202412010000/checkpoint-10000

# 任务启动入口
accelerate launch --config_file ${ACC_CFG} training_loops/table_structure_recognition/train_cycle_centernet.py \
  -c configs/table_structure_recognition/cycle_centernet/default_config.yaml \
  -o data.paths.train_data_dir=${TRAIN_DATA_DIR} \
  data.paths.val_data_dir=${VAL_DATA_DIR} \
  basic.output_dir=${OUTPUT_DIR} \
  training.epochs=150 \
  training.batch_size=8 \
  training.optimizer.type=AdamW \
  training.optimizer.learning_rate=0.0005 \
  training.optimizer.weight_decay=0.0001 \
  training.scheduler.type=cosine \
  training.scheduler.warmup.steps=0 \
  data.loader.num_workers=16 \
  data.loader.pin_memory=true \
  distributed.mixed_precision=fp16 \
  distributed.report_to=tensorboard \
  ema.enabled=true \
  ema.decay=0.999 \
  ema.start_step=1000 \
  checkpoint.save.steps=2000 \
  checkpoint.save.every_n_epoch=5 \
  checkpoint.save.keep_num=10 \
  checkpoint.validation.steps=1000 \
  checkpoint.resume.from_checkpoint=${CHECKPOINT_PATH} \
  model.backbone.depth=34 