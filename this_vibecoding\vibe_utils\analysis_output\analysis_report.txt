============================================================
COCO格式数据集分析报告
============================================================
文件路径: ./WTW-coco-test.json

1. 数据结构分析
------------------------------
顶层键: ['images', 'type', 'annotations', 'categories']
  images:
    类型: list
    长度: 3644
    第一个元素的键: ['id', 'file_name', 'width', 'height']
  type:
    类型: str
    长度: 9
  annotations:
    类型: list
    长度: 348000
    第一个元素的键: ['segmentation', 'logic_axis', 'area', 'iscrowd', 'ignore', 'image_id', 'bbox', 'category_id', 'id']
  categories:
    类型: list
    长度: 0

2. 图像信息分析
------------------------------
图像总数: 3644
宽度统计: 最小=180, 最大=4608, 平均=1382.7
高度统计: 最小=109, 最大=5081, 平均=1368.3
文件扩展名分布: {'.jpg': 3644}

3. 标注信息分析
------------------------------
标注总数: 348000
有标注的图像数: 3644
类别分布:
  类别ID 1: 348000 个标注

4. 类别信息分析
------------------------------
类别总数: 0
类别列表:
