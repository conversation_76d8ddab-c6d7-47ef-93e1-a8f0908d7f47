# modules/utils/visualization.py 模块分析

## 文件作用
该模块基于 PIL（Pillow），提供一系列通用的图像可视化工具函数，用于在图像上绘制透明叠层、标签、文本框等，便于标注和展示信息。

## 常量
- **DEFAULT_ALPHA** (float): 默认透明度，值为 0.65。
- **CATEGORY_COLORS** (Dict[str, str]): 预定义的类别到颜色名称映射，支持常见文档元素如 Text、Title、Figure 等。

## 函数清单

### 1. is_transparent(*colors) -> bool
- **用途**：判断一个或多个颜色值是否包含透明通道且 alpha < 255。
- **输入参数**：可变长参数 `colors`，每项可为 RGBA 元组或字符串。
- **输出**：`True` 表示至少有一个颜色透明，否则 `False`。
- **实现要点**：遍历参数，若遇到长度为 4 且第 4 位小于 255 的元组，则判定透明。

### 2. new_overlay(size) -> (Image.Image, ImageDraw.ImageDraw)
- **用途**：创建一个指定大小的透明覆盖层和对应的绘图对象。
- **输入**：`size` 二元组 `(width, height)`。
- **输出**：透明 RGBA 图像和 `ImageDraw` 对象。
- **实现**：调用 `Image.new('RGBA', size, (0,0,0,0))` 并返回其 Draw 实例。

### 3. get_font(font_path=None, font_size=12) -> ImageFont.FreeTypeFont
- **用途**：获取支持中文的字体对象。
- **输入**：`font_path`: 可选自定义字体文件路径；`font_size`：字体大小。
- **输出**：FreeTypeFont 对象。
- **实现步骤**：
  1. 若指定 `font_path`，直接加载。
  2. 否则尝试加载系统 `arial.ttf`，失败后回退 `ImageFont.load_default()`。

### 4. create_transparent_color(color, alpha=DEFAULT_ALPHA) -> Tuple[int, int, int, int]
- **用途**：生成带透明度的 RGBA 颜色。
- **输入**：`color`：颜色名称字符串或 RGB 元组；`alpha`：0~1 浮点透明度。
- **输出**：RGBA 四元组。
- **实现要点**：先将 `alpha` 缩放至 0~255 范围，再根据类型转换为 RGBA。

### 5. get_category_color(label, color_mapping=None) -> str
- **用途**：根据标签文本获取预设颜色。
- **输入**：`label`：标签文本；`color_mapping`：可选自定义映射，默认使用 `CATEGORY_COLORS`。
- **输出**：颜色名称字符串。
- **实现**：遍历映射，忽略大小写精确匹配 key，未命中返回默认 "blue"。

### 6. calculate_scale_factors(orig_width, orig_height, input_width=None, input_height=None) -> Tuple[float, float]
- **用途**：计算原图与输入图尺寸的缩放因子。
- **输入**：原始宽高 `orig_width`, `orig_height`；模型输入可选宽高 `input_width`, `input_height`。
- **输出**：`(scale_x, scale_y)`，若输入尺寸缺失或为0，返回 `(1.0, 1.0)`。
- **实现要点**：防止除零，直接用浮点除法。

### 7. scale_bbox(bbox, scale_x, scale_y) -> List[int]
- **用途**：对边界框坐标应用缩放。
- **输入**：`bbox` 四元素列表 `[x1,y1,x2,y2]`；`scale_x`, `scale_y`。
- **输出**：缩放后整数坐标列表。
- **实现要点**：校验 `bbox` 长度为4，然后按比例转换并取整。

### 8. draw_label(img, position, label, color, font, with_background=True) -> None
- **用途**：在图像指定位置绘制文字标签，可选半透明背景。
- **输入**：PIL 图像 `img`；位置 `(x,y)`；文本 `label`；RGBA 颜色 `color`；字体 `font`；`with_background` 是否绘制背景。
- **输出**：原图上直接绘制，无返回值。
- **实现步骤**：
  1. 根据透明度或背景需求决定使用 overlay。
  2. 计算文本尺寸，并绘制半透明矩形背景。
  3. 绘制文本并合成 overlay。

### 9. wrap_text_by_chars(text, font, max_width, max_lines) -> Tuple[List[str], str]
- **用途**：按字符宽度换行文本，返回可显示的行和剩余字符串。
- **输入**：`text`；`font` 对象；`max_width`；`max_lines`。
- **输出**：已换行行列表和剩余文本。
- **实现**：逐字累加字符宽度，超出 `max_width` 换行，直至达到 `max_lines`。

### 10. fit_text_to_box(text, box_width, box_height, font, min_font_size=8, max_font_size=72) -> Tuple[ImageFont.FreeTypeFont, str, float]
- **用途**：二分搜索最大小号，确保文本完整适应给定框。
- **输入**：文本 `text`；框宽高；`font` 基础对象；最小/最大字体大小。
- **输出**：最佳字体对象、带换行的文本、多行行高。
- **实现要点**：迭代调整字体大小，计算行高和最多行数，调用 `wrap_text_by_chars` 校验剩余文本。

### 11. draw_rect_text(img, bbox, bg_color=None, line_color=None, line_width=1, font=None, min_font_size=8, max_font_size=72, text=None, text_color=(0,0,0), label=None, label_color=(255,0,0)) -> None
- **用途**：绘制带文本的矩形框，并自适应文字大小和换行。
- **输入**：PIL 图像 `img`；边界框 `bbox`；背景色、边框色/宽度；字体及字体大小范围；文本及颜色；可选标签及其颜色。
- **输出**：原图上直接绘制。
- **实现步骤**：
  1. 选择 overlay 或直接在 `img` 上绘制。
  2. 绘制矩形填充与轮廓。
  3. 若 `text` 存在，调用 `fit_text_to_box` 并绘制多行文本。
  4. 合成 overlay。
  5. 若 `label` 存在，调用 `draw_label` 绘制。

### 12. draw_quad(img, quad_points, outline_color=None, fill_color=None, line_width=1) -> None
- **用途**：在图像上绘制四边形，支持透明填充和边框。
- **输入**：
  - `img`：PIL 图像对象
  - `quad_points`：四边形的四个顶点坐标，格式为 `[(x1, y1), (x2, y2), (x3, y3), (x4, y4)]`
  - `outline_color`：边框颜色，可以是颜色名称字符串或 RGB/RGBA 元组
  - `fill_color`：填充颜色，可以是颜色名称字符串或 RGB/RGBA 元组，`None` 表示不填充
  - `line_width`：边框线宽
- **输出**：原图上直接绘制，无返回值。
- **实现步骤**：
  1. 校验 `quad_points` 长度必须为 4。
  2. 根据颜色透明度决定是否使用 overlay。
  3. 使用 `draw.polygon` 绘制四边形。
  4. 如有透明色，合成 overlay 到原图。

## 函数依赖关系
```
is_transparent
    ↓
new_overlay ← draw_label ← draw_rect_text
         ← draw_quad
wrap_text_by_chars ← fit_text_to_box ← draw_rect_text
```

## __main__ 测试示例
模块末尾提供测试脚本，创建示例图像并调用 `draw_rect_text`，将结果保存到 `test.png`。
