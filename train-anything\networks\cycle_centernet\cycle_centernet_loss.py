#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 12:00
# <AUTHOR> <EMAIL>
# @FileName: cycle_centernet_loss.py

"""
Cycle-CenterNet损失函数实现

基于原Cycle-CenterNet项目中的损失函数实现，
适配train-anything项目的架构要求。

包含：
1. GaussianFocalLoss - 中心点热图损失
2. L1Loss - 偏移和向量回归损失
3. CycleCenterNetLoss - 组合损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional, Tuple


def gaussian_focal_loss(
    pred: torch.Tensor,
    target: torch.Tensor,
    alpha: float = 2.0,
    gamma: float = 4.0,
    eps: float = 1e-12
) -> torch.Tensor:
    """
    Gaussian Focal Loss核心计算函数
    
    基于CornerNet论文中的Focal Loss变体，专门用于处理高斯分布的热图目标。
    
    Args:
        pred: 预测值，形状为 (N, C, H, W)，值域为 [0, 1]
        target: 目标值，形状为 (N, C, H, W)，高斯分布热图
        alpha: 正样本的调制因子，默认为2.0
        gamma: 负样本的调制因子，默认为4.0
        eps: 数值稳定性常数
        
    Returns:
        损失张量，形状与输入相同
    """
    # 正样本权重：目标值为1的位置
    pos_weights = target.eq(1.0)
    
    # 负样本权重：使用 (1 - target)^gamma 进行调制
    neg_weights = (1 - target).pow(gamma)
    
    # 正样本损失：-log(pred) * (1 - pred)^alpha * pos_weights
    pos_loss = -(pred + eps).log() * (1 - pred).pow(alpha) * pos_weights
    
    # 负样本损失：-log(1 - pred) * pred^alpha * neg_weights
    neg_loss = -(1 - pred + eps).log() * pred.pow(alpha) * neg_weights
    
    return pos_loss + neg_loss


class GaussianFocalLoss(nn.Module):
    """
    Gaussian Focal Loss损失函数
    
    专门用于CenterNet风格的中心点热图预测，能够处理高斯分布的目标热图。
    
    Args:
        alpha: 正样本调制因子
        gamma: 负样本调制因子
        reduction: 损失归约方式 ('none', 'mean', 'sum')
        loss_weight: 损失权重
    """
    
    def __init__(
        self,
        alpha: float = 2.0,
        gamma: float = 4.0,
        reduction: str = 'mean',
        loss_weight: float = 1.0
    ):
        super(GaussianFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.loss_weight = loss_weight
    
    def forward(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        weight: Optional[torch.Tensor] = None,
        avg_factor: Optional[int] = None,
        reduction_override: Optional[str] = None
    ) -> torch.Tensor:
        """
        前向传播计算损失
        
        Args:
            pred: 预测热图，形状为 (N, C, H, W)
            target: 目标热图，形状为 (N, C, H, W)
            weight: 样本权重，可选
            avg_factor: 平均因子，用于归一化
            reduction_override: 覆盖默认的归约方式
            
        Returns:
            损失值
        """
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = reduction_override if reduction_override else self.reduction
        
        # 计算基础损失
        loss = gaussian_focal_loss(
            pred=pred,
            target=target,
            alpha=self.alpha,
            gamma=self.gamma
        )
        
        # 应用样本权重
        if weight is not None:
            loss = loss * weight
        
        # 应用归约
        if reduction == 'mean':
            if avg_factor is not None:
                loss = loss.sum() / avg_factor
            else:
                loss = loss.mean()
        elif reduction == 'sum':
            loss = loss.sum()
        # reduction == 'none' 时不做处理
        
        return self.loss_weight * loss


class L1Loss(nn.Module):
    """
    L1损失函数
    
    用于偏移回归和向量回归任务。
    
    Args:
        reduction: 损失归约方式
        loss_weight: 损失权重
    """
    
    def __init__(
        self,
        reduction: str = 'mean',
        loss_weight: float = 1.0
    ):
        super(L1Loss, self).__init__()
        self.reduction = reduction
        self.loss_weight = loss_weight
    
    def forward(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        weight: Optional[torch.Tensor] = None,
        avg_factor: Optional[int] = None,
        reduction_override: Optional[str] = None
    ) -> torch.Tensor:
        """
        前向传播计算L1损失
        
        Args:
            pred: 预测值
            target: 目标值
            weight: 样本权重
            avg_factor: 平均因子
            reduction_override: 覆盖默认的归约方式
            
        Returns:
            损失值
        """
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = reduction_override if reduction_override else self.reduction
        
        # 计算L1损失
        loss = F.l1_loss(pred, target, reduction='none')
        
        # 应用样本权重
        if weight is not None:
            loss = loss * weight
        
        # 应用归约
        if reduction == 'mean':
            if avg_factor is not None:
                loss = loss.sum() / avg_factor
            else:
                loss = loss.mean()
        elif reduction == 'sum':
            loss = loss.sum()
        
        return self.loss_weight * loss


class CycleCenterNetLoss(nn.Module):
    """
    Cycle-CenterNet组合损失函数
    
    组合多个损失函数用于完整的Cycle-CenterNet训练。
    当前版本为简化版，仅包含中心点热图损失。
    
    Args:
        heatmap_loss_cfg: 中心点热图损失配置
        offset_loss_cfg: 偏移损失配置（预留）
        center2vertex_loss_cfg: 中心到顶点损失配置（预留）
        vertex2center_loss_cfg: 顶点到中心损失配置（预留）
    """
    
    def __init__(
        self,
        heatmap_loss_cfg: Dict[str, Any] = None,
        offset_loss_cfg: Dict[str, Any] = None,
        center2vertex_loss_cfg: Dict[str, Any] = None,
        vertex2center_loss_cfg: Dict[str, Any] = None
    ):
        super(CycleCenterNetLoss, self).__init__()
        
        # 中心点热图损失（必需）
        if heatmap_loss_cfg is None:
            heatmap_loss_cfg = dict(alpha=2.0, gamma=4.0, loss_weight=1.0)
        
        self.heatmap_loss = GaussianFocalLoss(**heatmap_loss_cfg)
        
        # 其他损失（预留，当前版本暂不使用）
        self.offset_loss = None
        self.center2vertex_loss = None
        self.vertex2center_loss = None
        
        if offset_loss_cfg is not None:
            self.offset_loss = L1Loss(**offset_loss_cfg)
        
        if center2vertex_loss_cfg is not None:
            self.center2vertex_loss = L1Loss(**center2vertex_loss_cfg)
        
        if vertex2center_loss_cfg is not None:
            self.vertex2center_loss = L1Loss(**vertex2center_loss_cfg)
    
    def forward(
        self,
        predictions: Tuple[torch.Tensor, ...],
        targets: Dict[str, torch.Tensor],
        avg_factor: Optional[int] = None
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播计算组合损失
        
        Args:
            predictions: 模型预测结果元组
            targets: 目标字典，包含各种损失的目标值
            avg_factor: 平均因子
            
        Returns:
            损失字典
        """
        losses = {}
        
        # 中心点热图损失
        if len(predictions) >= 1:
            heatmap_pred = predictions[0]  # 第一个分支输出
            heatmap_target = targets['heatmap_target']

            loss_heatmap = self.heatmap_loss(
                pred=heatmap_pred,
                target=heatmap_target,
                avg_factor=avg_factor
            )
            losses['loss_heatmap'] = loss_heatmap
        
        # 偏移损失
        if self.offset_loss is not None and len(predictions) >= 2:
            offset_pred = predictions[1]  # 第二个分支输出
            offset_target = targets['offset_target']
            offset_weight = targets.get('offset_weight', None)

            loss_offset = self.offset_loss(
                pred=offset_pred,
                target=offset_target,
                weight=offset_weight,
                avg_factor=avg_factor * 2 if avg_factor else None  # 2个通道
            )
            losses['loss_offset'] = loss_offset
        
        if self.center2vertex_loss is not None and len(predictions) >= 3:
            c2v_pred = predictions[2]  # 第三个分支输出
            c2v_target = targets['center2vertex_target']
            c2v_weight = targets.get('center2vertex_weight', None)

            loss_c2v = self.center2vertex_loss(
                pred=c2v_pred,
                target=c2v_target,
                weight=c2v_weight,
                avg_factor=avg_factor * 8 if avg_factor else None  # 8个通道
            )
            losses['loss_center2vertex'] = loss_c2v
        
        if self.vertex2center_loss is not None and len(predictions) >= 4:
            v2c_pred = predictions[3]  # 第四个分支输出
            v2c_target = targets['vertex2center_target']
            v2c_weight = targets.get('vertex2center_weight', None)

            loss_v2c = self.vertex2center_loss(
                pred=v2c_pred,
                target=v2c_target,
                weight=v2c_weight,
                avg_factor=avg_factor * 8 if avg_factor else None  # 8个通道
            )
            losses['loss_vertex2center'] = loss_v2c
        
        return losses


def create_cycle_centernet_loss(
    version: str = "full",
    heatmap_loss_weight: float = 1.0,
    offset_loss_weight: float = 1.0,
    center2vertex_loss_weight: float = 1.0,
    vertex2center_loss_weight: float = 0.5
) -> CycleCenterNetLoss:
    """
    创建Cycle-CenterNet损失函数
    
    Args:
        version: 版本类型，"simple"为简化版，"full"为完整版
        heatmap_loss_weight: 中心点热图损失权重
        offset_loss_weight: 偏移损失权重
        center2vertex_loss_weight: 中心到顶点损失权重
        vertex2center_loss_weight: 顶点到中心损失权重
        
    Returns:
        损失函数实例
    """
    heatmap_loss_cfg = dict(
        alpha=2.0,
        gamma=4.0,
        loss_weight=heatmap_loss_weight
    )
    
    if version == "simple":
        return CycleCenterNetLoss(heatmap_loss_cfg=heatmap_loss_cfg)
    
    elif version == "full":
        offset_loss_cfg = dict(loss_weight=offset_loss_weight)
        center2vertex_loss_cfg = dict(loss_weight=center2vertex_loss_weight)
        vertex2center_loss_cfg = dict(loss_weight=vertex2center_loss_weight)
        
        return CycleCenterNetLoss(
            heatmap_loss_cfg=heatmap_loss_cfg,
            offset_loss_cfg=offset_loss_cfg,
            center2vertex_loss_cfg=center2vertex_loss_cfg,
            vertex2center_loss_cfg=vertex2center_loss_cfg
        )
    
    else:
        raise ValueError(f"Unsupported version: {version}. Choose 'simple' or 'full'.")
