#!/usr/bin/env python3
"""
COCO格式数据集分析脚本
用于分析类COCO格式的JSON标注文件结构，并提取样本数据
"""

import json
import os
import sys
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
import argparse


class COCOAnalyzer:
    """COCO格式数据集分析器"""
    
    def __init__(self, json_file_path: str):
        """
        初始化分析器
        
        Args:
            json_file_path: JSON标注文件路径
        """
        self.json_file_path = json_file_path
        self.data = None
        self.stats = {}
        
    def load_data(self) -> bool:
        """
        加载JSON数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            print(f"正在加载文件: {self.json_file_path}")
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print("文件加载成功!")
            return True
        except Exception as e:
            print(f"加载文件失败: {e}")
            return False
    
    def analyze_structure(self) -> Dict[str, Any]:
        """
        分析数据结构
        
        Returns:
            Dict: 结构分析结果
        """
        if not self.data:
            return {}
        
        structure = {}
        
        # 分析顶层键
        structure['top_level_keys'] = list(self.data.keys())
        
        # 分析每个顶层键的类型和长度
        for key in self.data.keys():
            value = self.data[key]
            key_info = {
                'type': type(value).__name__,
                'length': len(value) if hasattr(value, '__len__') else None
            }
            
            # 如果是列表，分析第一个元素的结构
            if isinstance(value, list) and len(value) > 0:
                first_item = value[0]
                if isinstance(first_item, dict):
                    key_info['first_item_keys'] = list(first_item.keys())
                    key_info['first_item_sample'] = first_item
                else:
                    key_info['first_item_type'] = type(first_item).__name__
                    key_info['first_item_sample'] = first_item
            
            structure[key] = key_info
        
        self.stats['structure'] = structure
        return structure
    
    def analyze_images(self) -> Dict[str, Any]:
        """
        分析图像信息
        
        Returns:
            Dict: 图像分析结果
        """
        if not self.data or 'images' not in self.data:
            return {}
        
        images = self.data['images']
        
        # 基本统计
        image_stats = {
            'total_count': len(images),
            'width_stats': {},
            'height_stats': {},
            'file_extensions': Counter(),
            'sample_images': []
        }
        
        # 收集尺寸信息
        widths = []
        heights = []
        
        for img in images:
            if 'width' in img:
                widths.append(img['width'])
            if 'height' in img:
                heights.append(img['height'])
            if 'file_name' in img:
                ext = os.path.splitext(img['file_name'])[1].lower()
                image_stats['file_extensions'][ext] += 1
        
        # 计算尺寸统计
        if widths:
            image_stats['width_stats'] = {
                'min': min(widths),
                'max': max(widths),
                'avg': sum(widths) / len(widths)
            }
        
        if heights:
            image_stats['height_stats'] = {
                'min': min(heights),
                'max': max(heights),
                'avg': sum(heights) / len(heights)
            }
        
        # 提取前5个图像作为样本
        image_stats['sample_images'] = images[:5]
        
        self.stats['images'] = image_stats
        return image_stats
    
    def analyze_annotations(self) -> Dict[str, Any]:
        """
        分析标注信息
        
        Returns:
            Dict: 标注分析结果
        """
        if not self.data or 'annotations' not in self.data:
            return {}
        
        annotations = self.data['annotations']
        
        # 基本统计
        ann_stats = {
            'total_count': len(annotations),
            'categories_count': Counter(),
            'images_with_annotations': set(),
            'sample_annotations': []
        }
        
        # 分析标注
        for ann in annotations:
            if 'category_id' in ann:
                ann_stats['categories_count'][ann['category_id']] += 1
            if 'image_id' in ann:
                ann_stats['images_with_annotations'].add(ann['image_id'])
        
        ann_stats['unique_images_with_annotations'] = len(ann_stats['images_with_annotations'])
        ann_stats['images_with_annotations'] = list(ann_stats['images_with_annotations'])[:10]  # 只保留前10个
        
        # 提取前5个标注作为样本
        ann_stats['sample_annotations'] = annotations[:5]
        
        self.stats['annotations'] = ann_stats
        return ann_stats
    
    def analyze_categories(self) -> Dict[str, Any]:
        """
        分析类别信息
        
        Returns:
            Dict: 类别分析结果
        """
        if not self.data or 'categories' not in self.data:
            return {}
        
        categories = self.data['categories']
        
        cat_stats = {
            'total_count': len(categories),
            'categories_list': categories
        }
        
        self.stats['categories'] = cat_stats
        return cat_stats
    
    def extract_samples(self, num_samples: int = 2) -> Dict[str, Any]:
        """
        提取指定数量的完整图像样本（包含图像信息和所有相关标注）
        
        Args:
            num_samples: 要提取的样本数量
            
        Returns:
            Dict: 包含完整样本信息的字典
        """
        if not self.data:
            return {}
        
        samples = {
            'sample_count': num_samples,
            'samples': []
        }
        
        images = self.data.get('images', [])
        annotations = self.data.get('annotations', [])
        categories = self.data.get('categories', [])
        
        # 创建图像ID到标注的映射
        image_to_annotations = defaultdict(list)
        for ann in annotations:
            if 'image_id' in ann:
                image_to_annotations[ann['image_id']].append(ann)
        
        # 创建类别ID到类别信息的映射
        category_map = {}
        for cat in categories:
            if 'id' in cat:
                category_map[cat['id']] = cat
        
        # 提取样本
        for i, image in enumerate(images[:num_samples]):
            sample = {
                'sample_index': i + 1,
                'image_info': image,
                'annotations': image_to_annotations.get(image.get('id', -1), []),
                'annotation_count': len(image_to_annotations.get(image.get('id', -1), [])),
                'categories_in_image': []
            }
            
            # 获取该图像中涉及的所有类别
            category_ids = set()
            for ann in sample['annotations']:
                if 'category_id' in ann:
                    category_ids.add(ann['category_id'])
            
            for cat_id in category_ids:
                if cat_id in category_map:
                    sample['categories_in_image'].append(category_map[cat_id])
            
            samples['samples'].append(sample)
        
        self.stats['samples'] = samples
        return samples
    
    def generate_report(self) -> str:
        """
        生成分析报告
        
        Returns:
            str: 格式化的分析报告
        """
        if not self.stats:
            return "没有可用的统计信息"
        
        report = []
        report.append("=" * 60)
        report.append("COCO格式数据集分析报告")
        report.append("=" * 60)
        report.append(f"文件路径: {self.json_file_path}")
        report.append("")
        
        # 结构分析
        if 'structure' in self.stats:
            structure = self.stats['structure']
            report.append("1. 数据结构分析")
            report.append("-" * 30)
            report.append(f"顶层键: {structure.get('top_level_keys', [])}")
            
            for key in structure.get('top_level_keys', []):
                if key in structure:
                    info = structure[key]
                    report.append(f"  {key}:")
                    report.append(f"    类型: {info.get('type', 'unknown')}")
                    if info.get('length') is not None:
                        report.append(f"    长度: {info['length']}")
                    if 'first_item_keys' in info:
                        report.append(f"    第一个元素的键: {info['first_item_keys']}")
            report.append("")
        
        # 图像分析
        if 'images' in self.stats:
            img_stats = self.stats['images']
            report.append("2. 图像信息分析")
            report.append("-" * 30)
            report.append(f"图像总数: {img_stats.get('total_count', 0)}")
            
            if 'width_stats' in img_stats:
                ws = img_stats['width_stats']
                report.append(f"宽度统计: 最小={ws.get('min', 0)}, 最大={ws.get('max', 0)}, 平均={ws.get('avg', 0):.1f}")
            
            if 'height_stats' in img_stats:
                hs = img_stats['height_stats']
                report.append(f"高度统计: 最小={hs.get('min', 0)}, 最大={hs.get('max', 0)}, 平均={hs.get('avg', 0):.1f}")
            
            if 'file_extensions' in img_stats:
                report.append(f"文件扩展名分布: {dict(img_stats['file_extensions'])}")
            report.append("")
        
        # 标注分析
        if 'annotations' in self.stats:
            ann_stats = self.stats['annotations']
            report.append("3. 标注信息分析")
            report.append("-" * 30)
            report.append(f"标注总数: {ann_stats.get('total_count', 0)}")
            report.append(f"有标注的图像数: {ann_stats.get('unique_images_with_annotations', 0)}")
            
            if 'categories_count' in ann_stats:
                report.append("类别分布:")
                for cat_id, count in ann_stats['categories_count'].most_common(10):
                    report.append(f"  类别ID {cat_id}: {count} 个标注")
            report.append("")
        
        # 类别分析
        if 'categories' in self.stats:
            cat_stats = self.stats['categories']
            report.append("4. 类别信息分析")
            report.append("-" * 30)
            report.append(f"类别总数: {cat_stats.get('total_count', 0)}")
            
            if 'categories_list' in cat_stats:
                report.append("类别列表:")
                for cat in cat_stats['categories_list'][:10]:  # 只显示前10个
                    report.append(f"  ID: {cat.get('id', 'N/A')}, 名称: {cat.get('name', 'N/A')}")
            report.append("")
        
        return "\n".join(report)
    
    def save_samples_to_file(self, output_file: str, num_samples: int = 2):
        """
        将样本数据保存到文件
        
        Args:
            output_file: 输出文件路径
            num_samples: 样本数量
        """
        samples = self.extract_samples(num_samples)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(samples, f, ensure_ascii=False, indent=2)
            print(f"样本数据已保存到: {output_file}")
        except Exception as e:
            print(f"保存样本数据失败: {e}")
    
    def run_full_analysis(self, output_dir: str = None, num_samples: int = 2):
        """
        运行完整分析
        
        Args:
            output_dir: 输出目录
            num_samples: 提取的样本数量
        """
        if not self.load_data():
            return
        
        print("开始分析数据结构...")
        self.analyze_structure()
        
        print("分析图像信息...")
        self.analyze_images()
        
        print("分析标注信息...")
        self.analyze_annotations()
        
        print("分析类别信息...")
        self.analyze_categories()
        
        print("提取样本数据...")
        self.extract_samples(num_samples)
        
        # 生成报告
        report = self.generate_report()
        print("\n" + report)
        
        # 保存结果
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存分析报告
            report_file = os.path.join(output_dir, "analysis_report.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n分析报告已保存到: {report_file}")
            
            # 保存样本数据
            samples_file = os.path.join(output_dir, "sample_data.json")
            self.save_samples_to_file(samples_file, num_samples)
            
            # 保存完整统计信息
            stats_file = os.path.join(output_dir, "full_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            print(f"完整统计信息已保存到: {stats_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='COCO格式数据集分析工具')
    parser.add_argument('--json_file', default='./WTW-coco-test.json', help='COCO格式的JSON标注文件路径')
    parser.add_argument('-o', '--output', help='输出目录路径', default='analysis_output')
    parser.add_argument('-n', '--num-samples', type=int, default=2, help='提取的样本数量')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json_file):
        print(f"错误: 文件不存在 - {args.json_file}")
        sys.exit(1)
    
    analyzer = COCOAnalyzer(args.json_file)
    analyzer.run_full_analysis(args.output, args.num_samples)


if __name__ == "__main__":
    main()
