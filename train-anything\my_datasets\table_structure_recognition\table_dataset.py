#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 10:30
# <AUTHOR> <EMAIL>
# @FileName: table_dataset.py

import os
import json
import random
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union

import cv2
import torch
import numpy as np
from PIL import Image
from torch.utils.data import Dataset

from .table_transforms import TableTransforms

# 简化版的set_seed函数，避免复杂的导入依赖
def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


class TableDataset(Dataset):
    """
    表格结构识别数据集基类

    支持两种数据组织方式：
    1. 集中式标注：单个JSON文件包含所有标注信息
    2. 分布式标注：每个图像对应一个JSON标注文件，支持分part的目录结构

    分布式标注的目录结构（新版本）：
    src_dir/
    ├── part_0001/
    │   ├── xxx.jpg/png
    │   ├── xxx.json 或 xxx_table_annotation.json
    │   └── ...
    ├── part_0002/
    │   ├── xxx.jpg/png
    │   ├── xxx.json 或 xxx_table_annotation.json
    │   └── ...
    └── ...

    注意：只有标注文件中 quality 字段为 "合格" 的样本才会被加载
    """

    def __init__(
        self,
        data_root: Union[str, List[str]],
        mode: str = 'train',
        annotation_file: Optional[str] = None,
        seed: int = -1,
        debug: bool = False,
        max_samples: Optional[int] = None,
        # 变换相关参数
        target_size: Tuple[int, int] = (512, 512),
        mean: List[float] = [0.485, 0.456, 0.406],
        std: List[float] = [0.229, 0.224, 0.225],
        to_rgb: bool = True,
        apply_transforms: bool = True
    ):
        """
        初始化表格数据集

        Args:
            data_root: 数据根目录，可以是单个路径字符串或路径列表
            mode: 模式 ('train', 'val', 'test')
            annotation_file: 标注文件路径，如果为None则使用默认路径
            seed: 随机种子
            debug: 是否为调试模式
            max_samples: 最大样本数量，用于调试
            target_size: 目标图像尺寸 (height, width)
            mean: 归一化均值
            std: 归一化标准差
            to_rgb: 是否转换为RGB格式
            apply_transforms: 是否应用数据变换
        """
        if seed == -1:
            seed = random.randint(1, 12580)
        set_seed(seed)

        # 处理多个数据目录
        if isinstance(data_root, str):
            self.data_roots = [Path(data_root)]
        else:
            self.data_roots = [Path(root) for root in data_root]

        # 保持向后兼容性
        self.data_root = self.data_roots[0]

        self.mode = mode
        self.debug = debug
        self.max_samples = max_samples

        # 变换相关参数
        self.apply_transforms = apply_transforms

        # 在内部实例化 TableTransforms
        if self.apply_transforms:
            self.transforms = TableTransforms(
                target_size=target_size,
                mean=mean,
                std=std,
                to_rgb=to_rgb,
                is_train=(mode == "train")
            )
        else:
            self.transforms = None

        # 默认使用分布式标注数据（每个图像对应一个JSON文件）
        self.annotations = self._load_distributed_annotations()

        if debug and max_samples:
            self.annotations = self.annotations[:max_samples]

        print(f"加载 {mode} 数据集: {len(self.annotations)} 个样本")

    def _load_distributed_annotations(self) -> List[Dict[str, Any]]:
        """
        加载分布式标注数据（图片和标注文件在同一目录）
        支持多个数据目录，只加载质量为"合格"的样本

        Returns:
            标注数据列表
        """
        annotations = []
        total_images = 0
        quality_invalid_count = 0

        # 遍历所有数据根目录
        for data_root_idx, data_root in enumerate(self.data_roots):
            if not data_root.exists():
                print(f"警告: 数据目录不存在，跳过: {data_root}")
                continue

            if self.debug:
                print(f"加载数据目录 {data_root_idx + 1}/{len(self.data_roots)}: {data_root}")

            # 遍历所有part目录（直接在data_root下）
            for part_dir in sorted(data_root.iterdir()):
                if not part_dir.is_dir() or not part_dir.stem.startswith("part_"):
                    continue

                part_name = part_dir.name

            # 遍历该part下的所有图像文件
            for image_file in part_dir.iterdir():
                if not image_file.is_file():
                    continue

                # 检查是否为图像文件
                if image_file.suffix.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                    continue

                total_images += 1

                # 查找对应的标注文件
                label_file = self._find_corresponding_label(image_file, part_dir)

                if label_file is None:
                    print(f"警告: 图片 {image_file.name} 没有找到对应的标注文件，跳过")
                    continue

                # 加载标注文件并检查质量
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        annotation_data = json.load(f)

                    # 检查质量字段
                    quality = annotation_data.get('quality', '')
                    if quality != '合格':
                        quality_invalid_count += 1
                        if self.debug:
                            print(f"跳过质量不合格的样本: {image_file.name}, quality={quality}")
                        continue

                    # 构建相对路径（包含数据根目录索引以区分不同来源）
                    if len(self.data_roots) > 1:
                        relative_image_path = f"data_{data_root_idx:02d}/{part_name}/{image_file.name}"
                    else:
                        relative_image_path = f"{part_name}/{image_file.name}"

                    # 标准化标注格式
                    if isinstance(annotation_data, dict):
                        # 如果标注文件包含完整信息
                        annotation_data['image_path'] = relative_image_path
                        annotation_data['part_name'] = part_name
                        annotation_data['data_root_idx'] = data_root_idx
                        annotation_data['absolute_image_path'] = str(image_file)  # 保存绝对路径用于实际加载
                        annotations.append(annotation_data)
                    else:
                        # 如果标注文件只包含cells信息
                        annotation = {
                            'image_path': relative_image_path,
                            'part_name': part_name,
                            'data_root_idx': data_root_idx,
                            'absolute_image_path': str(image_file),
                            'cells': annotation_data if isinstance(annotation_data, list) else []
                        }
                        annotations.append(annotation)

                except Exception as e:
                    print(f"警告: 加载标注文件失败，跳过: {label_file}, 错误: {e}")
                    continue

        print(f"多目录数据加载统计: 总图片数={total_images}, 质量不合格={quality_invalid_count}, 有效样本={len(annotations)}")

        if not annotations:
            raise ValueError(f"未找到任何有效的标注数据在目录: {self.data_roots}")

        return annotations

    def _find_corresponding_label(self, img_file: Path, part_dir: Path) -> Optional[Path]:
        """
        查找图片对应的标注文件
        优先匹配同名.json，如果不存在就匹配同名_table_annotation.json

        Args:
            img_file: 图片文件路径
            part_dir: 图片所在的part目录

        Returns:
            对应的标注文件路径，如果不存在则返回None
        """
        base_name = img_file.stem

        # 候选标注文件路径列表
        candidate_paths = [
            part_dir / f"{base_name}.json",  # 优先匹配同名.json
            part_dir / f"{base_name}_table_annotation.json"  # 回退匹配
        ]

        for label_path in candidate_paths:
            if label_path.exists():
                return label_path

        return None


    
    def _parse_cell_bbox(self, bbox: Dict[str, Any]) -> np.ndarray:
        """
        解析单元格边界框坐标
        
        Args:
            bbox: 边界框信息，包含四点坐标 {p1, p2, p3, p4}
            
        Returns:
            四点坐标数组，形状为 (4, 2)
        """
        points = []
        for i in range(1, 5):  # p1, p2, p3, p4
            point_key = f'p{i}'
            if point_key in bbox:
                point = bbox[point_key]
                if isinstance(point, dict) and 'x' in point and 'y' in point:
                    points.append([point['x'], point['y']])
                elif isinstance(point, (list, tuple)) and len(point) == 2:
                    points.append([point[0], point[1]])
                else:
                    raise ValueError(f"不支持的坐标格式: {point}")
            else:
                raise ValueError(f"缺少坐标点: {point_key}")
        
        return np.array(points, dtype=np.float32)
    
    def _get_cell_center(self, bbox_points: np.ndarray) -> Tuple[float, float]:
        """
        计算单元格中心点坐标
        
        Args:
            bbox_points: 四点坐标，形状为 (4, 2)
            
        Returns:
            中心点坐标 (cx, cy)
        """
        center_x = np.mean(bbox_points[:, 0])
        center_y = np.mean(bbox_points[:, 1])
        return float(center_x), float(center_y)
    
    def _get_cell_min_max_bbox(self, bbox_points: np.ndarray) -> Tuple[float, float, float, float]:
        """
        计算单元格的最小外接矩形
        
        Args:
            bbox_points: 四点坐标，形状为 (4, 2)
            
        Returns:
            最小外接矩形 (x_min, y_min, x_max, y_max)
        """
        x_min = float(np.min(bbox_points[:, 0]))
        y_min = float(np.min(bbox_points[:, 1]))
        x_max = float(np.max(bbox_points[:, 0]))
        y_max = float(np.max(bbox_points[:, 1]))
        return x_min, y_min, x_max, y_max
    
    def __len__(self) -> int:
        return len(self.annotations)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取单个样本
        
        Args:
            idx: 样本索引
            
        Returns:
            样本数据字典
        """
        annotation = self.annotations[idx]
        
        # 获取图像路径 - 优先使用绝对路径
        if 'absolute_image_path' in annotation:
            # 使用保存的绝对路径（多目录支持）
            image_path = Path(annotation['absolute_image_path'])
        elif 'image_path' in annotation:
            # 构建相对路径（向后兼容）
            image_path = annotation['image_path']
            if not os.path.isabs(image_path):
                # 对于多目录情况，使用对应的数据根目录
                data_root_idx = annotation.get('data_root_idx', 0)
                if data_root_idx < len(self.data_roots):
                    base_root = self.data_roots[data_root_idx]
                else:
                    base_root = self.data_root
                image_path = base_root / image_path
            else:
                image_path = Path(image_path)
        elif 'filename' in annotation:
            image_path = annotation['filename']
            if not os.path.isabs(image_path):
                image_path = self.data_root / image_path
            else:
                image_path = Path(image_path)
        else:
            raise ValueError(f"标注中缺少图像路径信息: {annotation}")

        if not image_path.exists():
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        
        # 加载图像
        image = self._load_image(image_path)
        
        # 解析单元格标注
        cells = annotation.get('cells', [])
        
        # 提取边界框和标签
        bboxes = []
        labels = []
        cell_centers = []
        
        for cell in cells:
            # 解析边界框
            bbox_points = self._parse_cell_bbox(cell['bbox'])
            
            # 计算中心点
            center_x, center_y = self._get_cell_center(bbox_points)
            cell_centers.append([center_x, center_y])
            
            # 计算最小外接矩形
            x_min, y_min, x_max, y_max = self._get_cell_min_max_bbox(bbox_points)
            bboxes.append([x_min, y_min, x_max, y_max])
            
            # 标签（目前简化为单一类别）
            labels.append(0)  # 表格单元格类别
        
        # 构建返回数据
        sample = {
            'image': image,
            'image_path': str(image_path),
            'bboxes': np.array(bboxes, dtype=np.float32) if bboxes else np.zeros((0, 4), dtype=np.float32),
            'labels': np.array(labels, dtype=np.int64) if labels else np.zeros((0,), dtype=np.int64),
            'cell_centers': np.array(cell_centers, dtype=np.float32) if cell_centers else np.zeros((0, 2), dtype=np.float32),
            'image_id': idx,
            'annotation': annotation
        }

        # 应用变换（如果启用）
        if self.apply_transforms and self.transforms is not None:
            sample = self.transforms(sample)

        return sample
    
    def _load_image(self, image_path: Path) -> np.ndarray:
        """
        加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            图像数组，BGR格式，形状为 (H, W, 3)
        """
        # 使用OpenCV加载图像（BGR格式）
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        return image
    
    def get_sample_info(self, idx: int) -> Dict[str, Any]:
        """
        获取样本信息（不加载图像）
        
        Args:
            idx: 样本索引
            
        Returns:
            样本信息字典
        """
        annotation = self.annotations[idx]
        
        info = {
            'index': idx,
            'table_ind': annotation.get('table_ind', idx),
            'image_path': annotation.get('image_path', annotation.get('filename', '')),
            'table_type': annotation.get('type', 'unknown'),
            'num_cells': len(annotation.get('cells', []))
        }
        
        return info
