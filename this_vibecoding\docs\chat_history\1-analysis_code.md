1. 本项目中集成了两个深度学习的子项目，请你根据规则 @0-parsecallchain.md ，以 @main.py 为起始代码，分析 LORE子项目@LORE-TSR 的整个调用链，分析结果保存到 @directory:this_vibecoding/docs/1-analysis_code目录下的readme_LORE_callchain.md中。

2. 本项目中集成了两个深度学习的子项目，请你根据规则 @0-parsecallchain.md ，以 @main.py 为起始代码，分析 LORE子项目@LORE-TSR 的整个调用链(需要详细到涉及的具体网络模块、损失函数、优化器、调度器等等)，分析结果保存到 @directory:this_vibecoding/docs/1-analysis_code目录下的readme_LORE_callchain.md中

3. 请整理上述关键核心内容并更新你的记忆库，以便后续快速了解 LORE-TSR的项目细节以及项目架构迁移

4. 请整理上述关键核心内容并更新你的记忆库，以便后续快速了解 LORE-TSR的项目细节以及项目架构迁移



------------------------------------------

4. 本项目中集成了两个深度学习的子项目（LORE-TSR和train-anything），请你根据规则 @d:\workspace\projects\TSRTransplantation/this_vibecoding\.windsurf\rules\0-parsecallchain.md， 
    1. 结合训练脚本： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\scripts\train\train_wireless_arcres.sh , 以 @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\main.py 为起始代码，结合项目的配置文件： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\lib\opts.py ；
    2. 分析 LORE子项目 @d:\workspace\projects\TSRTransplantation/LORE-TSR/ 的整个调用链；
    3. 并将分析结果保存到 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code/ 目录下的readme_LORE_callchain.md中。


5. 为了便于后续进行架构的迁移，我希望你能根据规则 @d:\workspace\projects\TSRTransplantation/this_vibecoding\.windsurf\rules\0-parsecallchain.md ，在 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code\readme_LORE_callchain.md 的当前基础上继续补充关于：数据加载链路分析（很关键、因为迁移的目标架构的数据标注方式和数据集的组织方式跟LORE-TSR源项目有很大差异）、模型架构分析（不必过于深入）、损失函数分析（入口、组成、被调用点）、优化器分析、学习率调度的部分；你可以结合下面的文件继续展开分析：
    1. 训练脚本： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\scripts\train\train_wireless_arcres.sh ,  2. 起始代码： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\main.py ，3. 项目的配置文件： @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\lib\opts.py ； 4. 将分析后的结果继续补充到 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code\readme_LORE_callchain.md 中

6. 请结合上面的分析，同时更新 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code\readme_LORE_callchain.md 中的目录结构和实体关系图（如有必要）

7. 还有一个待完善的点：基于训练脚本 @d:\workspace\projects\TSRTransplantation/LORE-TSR\src\scripts\train\train_wireless_arcres.sh 中的参数和前面的调用链分析，在 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code\readme_LORE_callchain.md 的文件目录结构部分对目录或文件进行标记（以示跟其他相关文件的区别）

--------------------------------------------------------
------------------------
--------------------------------------------------------

8. 本项目中集成了两个深度学习的子项目（LORE-TSR和train-anything），请你根据规则 @d:\workspace\projects\TSRTransplantation/this_vibecoding\.windsurf\rules\0-parsecallchain.md，  
    1. 以 @d:\workspace\projects\TSRTransplantation/train-anything\training_loops\table_structure_recognition\train_cycle_centernet_ms.py 为起始代码，结合其对应的项目的配置文件： @d:\workspace\projects\TSRTransplantation/train-anything\configs\table_structure_recognition\cycle_centernet\cycle_centernet_ms_config.yaml ； 
    2. 分析 @d:\workspace\projects\TSRTransplantation/train-anything/ 子项目中的关于Cycle-CenterNet-MS的整个调用链； 
    3. 并将分析结果保存到 @d:\workspace\projects\TSRTransplantation/this_vibecoding\docs\1-analysis_code/ 目录下的readme_cycle-centernet-ms_callchain.md中。











    