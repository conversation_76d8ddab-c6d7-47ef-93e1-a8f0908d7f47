#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.:src

# 初始化accelerate配置，以及安装必要依赖
pip install omegaconf>=2.3.0 tqdm Pillow opencv-python
export ACC_CFG=tsr_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space

# 数据路径设置
TRAIN_DATA_DIR=${TRANSPACE_DIR}/datasets/table_structure/train
VAL_DATA_DIR=${TRANSPACE_DIR}/datasets/table_structure/val
OUTPUT_DIR=${TRANSPACE_DIR}/training_outputs/cycle_centernet/debug_v$(date +%Y%m%d%H%M)

# 任务启动入口
accelerate launch --config_file ${ACC_CFG} training_loops/table_structure_recognition/train_cycle_centernet.py \
  -c configs/table_structure_recognition/cycle_centernet/default_config.yaml \
  -o data.paths.train_data_dir=${TRAIN_DATA_DIR} \
  data.paths.val_data_dir=${VAL_DATA_DIR} \
  basic.output_dir=${OUTPUT_DIR} \
  basic.debug=true \
  training.epochs=5 \
  training.batch_size=2 \
  training.optimizer.type=AdamW \
  training.optimizer.learning_rate=0.001 \
  training.optimizer.weight_decay=0.0001 \
  training.scheduler.type=cosine \
  training.scheduler.warmup.steps=100 \
  data.loader.num_workers=4 \
  data.loader.pin_memory=true \
  data.processing.max_samples=100 \
  distributed.mixed_precision=fp16 \
  distributed.report_to=tensorboard \
  ema.enabled=true \
  ema.decay=0.999 \
  ema.start_step=50 \
  checkpoint.save.steps=100 \
  checkpoint.save.every_n_epoch=1 \
  checkpoint.save.keep_num=2 \
  checkpoint.validation.steps=50 \
  checkpoint.validation.num_batches=5 \
  model.backbone.depth=18 