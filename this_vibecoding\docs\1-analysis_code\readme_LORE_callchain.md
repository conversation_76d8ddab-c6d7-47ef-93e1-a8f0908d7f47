# LORE-TSR 项目调用链分析

## 调用链（Call Chain）

### 节点：`main`
- **文件路径**：LORE-TSR/src/main.py
- **功能说明**：项目的主入口函数，负责初始化训练环境、创建模型和数据集、执行训练循环
- **输入参数**：
  - `opt`: 从opts.py解析的配置对象，包含所有训练参数如学习率、批次大小、模型架构等
- **输出说明**：无返回值，执行完整的训练流程并保存模型
- **节点流程可视化**:
```mermaid
flowchart TD
    A[解析命令行参数] --> B[设置随机种子和CUDA]
    B --> C[获取数据集类]
    C --> D[更新配置信息]
    D --> E[创建Logger]
    E --> F[创建模型]
    F --> G[创建Processor]
    G --> H[创建优化器]
    H --> I[创建Trainer]
    I --> J[加载预训练模型]
    J --> K[设置设备]
    K --> L[创建数据加载器]
    L --> M{是否测试模式}
    M -->|是| N[执行验证]
    M -->|否| O[训练循环]
    O --> P[保存模型]
```

### 节点：`get_dataset`
- **文件路径**：LORE-TSR/src/lib/datasets/dataset_factory.py
- **功能说明**：数据集工厂函数，根据数据集名称和任务类型动态创建数据集类
- **输入参数**：
  - `dataset`: 数据集名称，如'table_mid'
  - `task`: 任务类型，如'ctdet_mid'
- **输出说明**：返回组合了数据集基类和采样类的Dataset类
- **节点流程可视化**:
```mermaid
flowchart TD
    A[接收dataset和task参数] --> B[从dataset_factory获取数据集基类]
    B --> C[从_sample_factory获取采样类]
    C --> D[动态创建组合类Dataset]
    D --> E[返回Dataset类]
```

### 节点：`create_model`
- **文件路径**：LORE-TSR/src/lib/models/model.py
- **功能说明**：模型工厂函数，根据架构名称创建对应的神经网络模型
- **输入参数**：
  - `arch`: 模型架构名称，如'resfpnhalf_18'
  - `heads`: 输出头配置字典，包含各种任务头的通道数
  - `head_conv`: 头部卷积层通道数
- **输出说明**：返回构建好的PyTorch模型实例
- **节点流程可视化**:
```mermaid
flowchart TD
    A[解析架构名称] --> B[提取层数信息]
    B --> C[从_model_factory获取构建函数]
    C --> D[调用构建函数创建模型]
    D --> E[返回模型实例]
```

### 节点：`Processor.__init__`
- **文件路径**：LORE-TSR/src/lib/models/classifier.py
- **功能说明**：逻辑位置处理器的初始化，负责创建Transformer和位置嵌入组件
- **输入参数**：
  - `opt`: 配置对象，包含模型参数如hidden_size、tsfm_layers等
- **输出说明**：初始化完成的Processor实例
- **节点流程可视化**:
```mermaid
flowchart TD
    A[检查是否启用stacking] --> B{wiz_stacking?}
    B -->|是| C[创建Stacker组件]
    B -->|否| D[创建Transformer组件]
    C --> D
    D --> E[创建位置嵌入层]
    E --> F[保存配置对象]
```

### 节点：`train_factory[opt.task]`
- **文件路径**：LORE-TSR/src/lib/trains/train_factory.py
- **功能说明**：训练器工厂，根据任务类型返回对应的训练器类
- **输入参数**：
  - `task`: 任务类型，如'ctdet_mid'
- **输出说明**：返回对应的训练器类（CtdetTrainer）
- **节点流程可视化**:
```mermaid
flowchart TD
    A[接收任务类型] --> B[查找train_factory字典]
    B --> C[返回CtdetTrainer类]
```

### 节点：`trainer.train`
- **文件路径**：LORE-TSR/src/lib/trains/base_trainer.py
- **功能说明**：执行一个epoch的训练，包括前向传播、损失计算、反向传播和参数更新
- **输入参数**：
  - `epoch`: 当前训练轮次
  - `data_loader`: 训练数据加载器
- **输出说明**：返回训练损失统计字典和结果
- **节点流程可视化**:
```mermaid
sequenceDiagram
    participant T as Trainer
    participant M as ModelWithLoss
    participant P as Processor
    participant L as Loss

    T->>M: forward(epoch, batch)
    M->>M: model(batch['input'])
    M->>P: processor(outputs, batch)
    P-->>M: logic_axis
    M->>L: loss(epoch, outputs, batch, logic_axis)
    L-->>M: loss, loss_stats
    M-->>T: output, loss, loss_stats
    T->>T: optimizer.step()
```

---

## 整体用途（Overall Purpose）

LORE-TSR项目实现了一个基于深度学习的表格结构识别系统，主要功能包括：

1. **表格检测与识别**：使用CenterNet架构检测表格中的单元格位置
2. **逻辑位置推理**：通过Transformer网络推理单元格的逻辑坐标（行列位置）
3. **端到端训练**：结合检测和逻辑推理的联合训练框架
4. **多种表格类型支持**：支持有线表格和无线表格的结构识别

该系统解决了传统表格识别中物理位置到逻辑位置映射的关键问题，在文档理解、信息提取等场景中具有重要应用价值。

---

## 目录结构（Directory Structure）

```
LORE-TSR/src/
├── main.py                           # 主入口文件
├── lib/
│   ├── opts.py                       # 配置参数解析
│   ├── datasets/
│   │   ├── dataset_factory.py        # 数据集工厂
│   │   ├── dataset/
│   │   │   └── table_mid.py          # 表格数据集实现
│   │   └── sample/
│   │       └── ctdet.py              # CenterNet采样实现
│   ├── models/
│   │   ├── model.py                  # 模型工厂和加载
│   │   ├── classifier.py             # Processor和Transformer实现
│   │   └── networks/                 # 网络架构定义
│   └── trains/
│       ├── train_factory.py          # 训练器工厂
│       ├── base_trainer.py           # 基础训练器
│       └── ctdet.py                  # CenterNet训练器
└── scripts/
    └── train/
        └── train_wireless_arcres.sh  # 训练脚本
```

---

## 调用时序图（Mermaid 格式）

### 调用顺序图（sequenceDiagram）

```mermaid
sequenceDiagram
    participant Main as main.py
    participant Opts as lib/opts.py
    participant DF as lib/datasets/dataset_factory.py
    participant MF as lib/models/model.py
    participant CF as lib/models/classifier.py
    participant TF as lib/trains/train_factory.py
    participant BT as lib/trains/base_trainer.py
    participant CT as lib/trains/ctdet.py

    Main->>Opts: opts().parse()
    Opts-->>Main: opt配置对象

    Main->>DF: get_dataset(opt.dataset, opt.task)
    DF-->>Main: Dataset类

    Main->>Opts: update_dataset_info_and_set_heads(opt, Dataset)
    Opts-->>Main: 更新后的opt

    Main->>MF: create_model(opt.arch, opt.heads, opt.head_conv)
    MF-->>Main: model实例

    Main->>CF: Processor(opt)
    CF-->>Main: processor实例

    Main->>TF: train_factory[opt.task]
    TF-->>Main: CtdetTrainer类

    Main->>CT: Trainer(opt, model, optimizer, processor)
    CT->>BT: super().__init__(opt, model, optimizer, processor)
    BT-->>CT: 初始化完成
    CT-->>Main: trainer实例

    loop 训练循环
        Main->>CT: trainer.train(epoch, train_loader)
        CT->>BT: run_epoch('train', epoch, data_loader)

        loop 批次循环
            BT->>BT: model_with_loss(epoch, batch)
            Note over BT: 前向传播、损失计算、反向传播
        end

        BT-->>CT: 训练统计结果
        CT-->>Main: log_dict_train

        alt 验证间隔
            Main->>CT: trainer.val(epoch, val_loader)
            CT->>BT: run_epoch('val', epoch, data_loader)
            BT-->>CT: 验证统计结果
            CT-->>Main: log_dict_val
        end
    end
```

### 实体关系图（erDiagram）

```mermaid
erDiagram
    MAIN {
        function main
        object opt
        class Dataset
        object model
        object processor
        object trainer
        object optimizer
    }

    OPTS {
        class opts
        function parse
        function update_dataset_info_and_set_heads
        dict default_dataset_info
    }

    DATASET_FACTORY {
        dict dataset_factory
        dict sample_factory
        function get_dataset
    }

    MODEL_FACTORY {
        dict model_factory
        function create_model
        function load_model
    }

    PROCESSOR {
        class Processor
        object tsfm_axis
        object stacker
        object position_embeddings
    }

    TRAINER {
        class CtdetTrainer
        class BaseTrainer
        class ModleWithLoss
        object loss
        object loss_stats
    }

    MAIN ||--|| OPTS : "解析配置"
    MAIN ||--|| DATASET_FACTORY : "创建数据集"
    MAIN ||--|| MODEL_FACTORY : "创建模型"
    MAIN ||--|| PROCESSOR : "创建处理器"
    MAIN ||--|| TRAINER : "创建训练器"
    TRAINER ||--|| PROCESSOR : "包含处理器"
    TRAINER ||--|| MODEL_FACTORY : "包含模型"
```

