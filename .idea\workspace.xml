<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="497205d3-f3ef-487b-b5a2-bdf4de0b914b" name="Changes" comment="4. 迁移 `LORE-TSR` 中负责创建和加载模型的工厂函数 `create_model`。这是连接配置文件和模型实体的关键“胶水代码”。" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/train-anything" value="dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/train-anything" />
    <option name="RESET_MODE" value="HARD" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;lanxin&quot;,
      &quot;fullname&quot;: &quot;<EMAIL>&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.liebaopay.com/datax/train-anything.git&quot;,
    &quot;second&quot;: &quot;79f8ca7c-dbe3-4838-b94e-982156044d75&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zi65llJiCyZE8jiZDolOl2Ngd4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.mappings": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.symlinks": "true",
    "WebServerToolWindowPanel.toolwindow.show.date": "false",
    "WebServerToolWindowPanel.toolwindow.show.permissions": "false",
    "WebServerToolWindowPanel.toolwindow.show.size": "false",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/workspace/projects/TSRTransplantation/train-anything/vibe_coding_lore/2-migration_lore",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\train-anything\vibe_coding_lore\2-migration_lore" />
      <recent name="D:\workspace\projects\TSRTransplantation\train-anything\vibecoding_lore" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\train-anything\vibe_coding_lore\2-migration_lore\migration_reports" />
      <recent name="D:\workspace\projects\TSRTransplantation\this_vibecoding\docs\bak" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="497205d3-f3ef-487b-b5a2-bdf4de0b914b" name="Changes" comment="" />
      <created>1752201365992</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752201365992</updated>
      <workItem from="1752201367127" duration="5037000" />
      <workItem from="1752491451775" duration="5581000" />
      <workItem from="1752502252347" duration="439000" />
      <workItem from="1752502705545" duration="490000" />
      <workItem from="1752503605268" duration="259000" />
      <workItem from="1752504066504" duration="11995000" />
      <workItem from="1752556316978" duration="16394000" />
      <workItem from="1752589127912" duration="174000" />
      <workItem from="1752589760086" duration="64000" />
      <workItem from="1752589839572" duration="1506000" />
      <workItem from="1752591435670" duration="24470000" />
      <workItem from="1752721093439" duration="296000" />
    </task>
    <task id="LOCAL-00001" summary="1. 构建LORE项目迁移的需求规划文档和渐进式迭代步骤一（制定蓝本、创建目录）">
      <option name="closed" value="true" />
      <created>1752507922842</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752507922842</updated>
    </task>
    <task id="LOCAL-00002" summary="1. 构建LORE项目迁移的需求规划文档和渐进式迭代步骤一（制定蓝本、创建目录）">
      <option name="closed" value="true" />
      <created>1752507970843</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752507970843</updated>
    </task>
    <task id="LOCAL-00003" summary="1. 构建LORE项目迁移的需求规划文档完成渐进式迭代步骤一（制定蓝本、创建目录、输出报告）">
      <option name="closed" value="true" />
      <created>1752653297174</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752653297174</updated>
    </task>
    <task id="LOCAL-00004" summary="2. 复制并隔离编译依赖项。具体是将 LORE-TSR 项目中需要手动编译的外部依赖 `DCNv2` 和 `nms.pyx` 原封不动地复制到 train-anything 框架的 `external/lore_tsr/` 目录下">
      <option name="closed" value="true" />
      <created>1752658928724</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752658928724</updated>
    </task>
    <task id="LOCAL-00005" summary="3. 将实现核心算法的文件（不包括模型创建/加载的工厂文件）近乎逐字地复制到 `train-anything` 的 `networks/lore_tsr/` 目录中。这一步迁移了模型的计算核心，是后续适配和重构的基础。">
      <option name="closed" value="true" />
      <created>1752669555658</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752669555658</updated>
    </task>
    <task id="LOCAL-00006" summary="4. 迁移 `LORE-TSR` 中负责创建和加载模型的工厂函数 `create_model`。这是连接配置文件和模型实体的关键“胶水代码”。">
      <option name="closed" value="true" />
      <created>1752673906391</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752673906391</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feat/train_tsr_xelawk" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="114" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="82" />
                  <entry key="Table.GitHub.CommitStatus.ColumnIdWidth" value="76" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feat/train_tsr_lore" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="1. 构建LORE项目迁移的需求规划文档和渐进式迭代步骤一（制定蓝本、创建目录）" />
    <MESSAGE value="tmp save" />
    <MESSAGE value="drop" />
    <MESSAGE value="1. 构建LORE项目迁移的需求规划文档完成渐进式迭代步骤一（制定蓝本、创建目录、输出报告）" />
    <MESSAGE value="2. 复制并隔离编译依赖项。具体是将 LORE-TSR 项目中需要手动编译的外部依赖 `DCNv2` 和 `nms.pyx` 原封不动地复制到 train-anything 框架的 `external/lore_tsr/` 目录下" />
    <MESSAGE value="3. 将实现核心算法的文件（不包括模型创建/加载的工厂文件）近乎逐字地复制到 `train-anything` 的 `networks/lore_tsr/` 目录中。这一步迁移了模型的计算核心，是后续适配和重构的基础。" />
    <MESSAGE value="4. 迁移 `LORE-TSR` 中负责创建和加载模型的工厂函数 `create_model`。这是连接配置文件和模型实体的关键“胶水代码”。" />
    <MESSAGE value="trouble-step5" />
    <option name="LAST_COMMIT_MESSAGE" value="trouble-step5" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/main.py</url>
          <line>98</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/lib/datasets/dataset/table_mid.py</url>
          <line>25</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>