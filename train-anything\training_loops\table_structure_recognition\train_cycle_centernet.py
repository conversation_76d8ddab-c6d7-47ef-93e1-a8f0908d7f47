#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 14:00
# <AUTHOR> <EMAIL>
# @FileName: train_cycle_centernet.py

"""
Cycle-CenterNet表格结构识别训练脚本

基于accelerate框架的分布式训练实现，支持混合精度训练和多GPU训练。
"""

import os
import re
import math
import json
import random
import datetime
import warnings
from pathlib import Path
from itertools import islice
from typing import Tuple, List

import torch
import torch.nn as nn
import torch.utils.data
from tqdm import tqdm

from accelerate.logging import get_logger
import modules.utils.torch_utils as torch_utils
from modules.utils.log import create_file_logger
from modules.utils.torch_utils import EMAHandler
from modules.utils.optimization import get_scheduler
from modules.proj_cmd_args.cycle_centernet.args import parse_args
from modules.utils.train_utils import prepare_training_enviornment_v2

from networks.cycle_centernet import (
    create_cycle_centernet_loss,
    create_cycle_centernet_model
)
from my_datasets.table_structure_recognition import (
    collate_fn,
    TableDataset,
    prepare_targets,
)

# 使用新的配置系统
config = parse_args()
logger = get_logger(__name__)
file_logger_path = Path(os.path.join(config.basic.output_dir, "logs", "monitors.log"))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, "DEBUG")
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


def get_optimizer(config, params_to_opt, optimizer_ckpt):
    """
    创建优化器，统一的优化器创建函数

    Args:
        config: 配置对象
        params_to_opt: 需要优化的参数
        optimizer_ckpt: 优化器检查点路径

    Returns:
        torch.optim.Optimizer: 创建的优化器
    """
    optimizer_config = config.training.optimizer

    if optimizer_config.type == "Adam" or optimizer_config.adam.use_adam:
        optimizer_class = torch.optim.Adam
    elif optimizer_config.type == "AdamW":
        if optimizer_config.adam.use_8bit_adamw:
            try:
                import bitsandbytes as bnb
            except ImportError:
                raise ImportError(
                    "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
                )
            optimizer_class = bnb.optim.AdamW8bit
        else:
            optimizer_class = torch.optim.AdamW
    else:
        optimizer = torch.optim.SGD(
            params_to_opt,
            lr=optimizer_config.learning_rate,
            momentum=optimizer_config.momentum,
            weight_decay=optimizer_config.weight_decay,
        )

        if optimizer_ckpt is not None:
            optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
            optimizer.load_state_dict(optimizer_state_dict)

        return optimizer

    # Adam/AdamW优化器
    optimizer = optimizer_class(
        params_to_opt,
        lr=optimizer_config.learning_rate,
        betas=(optimizer_config.adam.beta1, optimizer_config.adam.beta2),
        weight_decay=optimizer_config.adam.weight_decay,
        eps=optimizer_config.adam.epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    return optimizer


def manage_checkpoints(output_dir, n_checkpoints_to_keep):
    """
    管理检查点，删除旧的检查点

    Args:
        output_dir: 输出目录
        n_checkpoints_to_keep: 保留的检查点数量
    """
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        msg = f"Old version checkpoints to delete: {checkpoints_to_delete}"
        logger.info(msg)
        file_logger.info(msg)

        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def prepare_dataloaders(config, mode: str, train_batch_size_per_device: int, seed: int = -1):
    """
    准备数据加载器

    Args:
        config: 配置对象
        mode: 模式 ('train', 'val')
        train_batch_size_per_device: 每设备批次大小
        seed: 随机种子

    Returns:
        数据集和数据加载器列表
    """
    debug = config.basic.debug
    if debug:
        msg = f"Using debug mode to load {mode} dataset with small size"
        logger.info(msg)
        file_logger.info(msg)

    datasets: List[TableDataset] = []
    loaders: List[Tuple[str, torch.utils.data.DataLoader]] = []

    # 确定数据目录
    if mode == "train":
        data_dir = config.data.paths.train_data_dir
    elif mode == "val":
        data_dir = config.data.paths.val_data_dir
    else:
        raise ValueError(f"Unsupported mode: {mode}")

    if data_dir is None:
        logger.warning(f"No {mode} data directory provided")
        return datasets, loaders

    # 创建数据集（内部自动实例化TableTransforms）
    dataset = TableDataset(
        mode=mode,
        seed=seed,
        debug=debug,
        data_root=data_dir,
        max_samples=config.data.processing.max_samples if debug else None,
        target_size=tuple(config.data.processing.image_size),
        mean=config.data.processing.normalize.mean,
        std=config.data.processing.normalize.std,
        to_rgb=config.data.processing.normalize.to_rgb,
        apply_transforms=True  # 启用变换
    )
    datasets.append(dataset)

    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        shuffle=(mode == "train"),
        batch_size=train_batch_size_per_device,
        num_workers=config.data.loader.num_workers,
        pin_memory=config.data.loader.pin_memory,
        collate_fn=collate_fn
    )
    
    loaders.append((f"Table{mode.capitalize()}", dataloader))
    
    return datasets, loaders


def save_state(
    save_dir,
    model,
    optimizer,
    lr_scheduler,
    accelerator,
    n_checkpoints_to_keep=None,
    ema_handler=None,
):
    """
    保存训练状态，对齐到Lama的实现

    Args:
        save_dir: 保存目录
        model: 模型
        optimizer: 优化器
        lr_scheduler: 学习率调度器
        accelerator: accelerate对象
        n_checkpoints_to_keep: 保留的检查点数量
        ema_handler: EMA处理器
    """
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    lr_scheduler = accelerator.unwrap_model(lr_scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "pytorch_model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, "optimizer.bin"))
    torch.save(lr_scheduler.state_dict(), os.path.join(save_dir, "scheduler.bin"))

    if ema_handler is not None:
        ema_handler.save(os.path.join(save_dir, "pytorch_model_ema.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep)


def save_best_checkpoints(
    config,
    accelerator,
    model,
    ema_handler,
    optimizer,
    lr_scheduler,
    global_step,
    val_metrics,
    record_dump_path,
    current_best_record
):
    """
    保存最佳模型检查点

    Args:
        config: 参数
        accelerator: accelerate对象
        model: 模型
        ema_handler: EMA处理器
        optimizer: 优化器
        lr_scheduler: 学习率调度器
        global_step: 全局步数
        val_metrics: 验证指标
        record_dump_path: 记录文件路径
        current_best_record: 当前最佳记录
    """
    found_best_model = False
    avg_loss = val_metrics
    tmp_record = {"avg_loss": avg_loss}

    # 损失越小越好
    if avg_loss <= current_best_record["avg_loss"]:
        found_best_model = True

    if found_best_model:
        current_best_record.update(tmp_record)
        with open(record_dump_path, 'w', encoding='utf-8') as f:
            data = dict()
            data.update(current_best_record)
            data['checkpoint_step'] = global_step
            json.dump(data, f, ensure_ascii=False, indent=4)

        msg = f"Found best model: {data}"
        logger.info(msg)
        file_logger.info(msg)

        save_state(
            Path(record_dump_path).parent,
            model,
            optimizer,
            lr_scheduler,
            accelerator,
            ema_handler=ema_handler,
        )


def log_validation(
    config,
    model: nn.Module,
    ema_handler,
    global_step: int,
    accelerator,
    weight_dtype: torch.dtype,
    val_loaders: List[Tuple[str, torch.utils.data.DataLoader]]
) -> float:
    """
    验证日志记录，对齐到Lama的实现

    Args:
        config: 参数
        model: 模型
        ema_handler: EMA处理器
        global_step: 全局步数
        accelerator: accelerate对象
        weight_dtype: 权重数据类型
        val_loaders: 验证数据加载器列表

    Returns:
        验证指标
    """
    if not val_loaders:
        return float('inf')  # 返回无穷大表示没有验证数据

    model = accelerator.unwrap_model(model)
    if config.ema.enabled and ema_handler is not None:
        ema_handler.store(model)
        ema_handler.apply_to(model)
    model.eval()

    print()
    msg = f"Running metrics for step-{global_step}..."
    logger.info(msg)
    file_logger.info(msg)

    # 创建损失函数, 固定使用full版本
    criterion = create_cycle_centernet_loss(version="full")

    total_loss = 0.0
    total_samples = 0

    # TODO: 验证评估会比较耗时, 可以指定数量
    if config.checkpoint.validation.num_batches is not None and isinstance(config.checkpoint.validation.num_batches, int):
        num_batch_to_eval = config.checkpoint.validation.num_batches
        test_pbar = tqdm(total=num_batch_to_eval, desc='evaluating...')
        dataloader_steps = islice(walk_dataloaders(val_loaders), 0, num_batch_to_eval)
    else:
        test_dataloader_total_steps = sum(len(loader) for flag, loader in val_loaders)
        test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')
        dataloader_steps = walk_dataloaders(val_loaders)

    with torch.no_grad():
        for step, (flag, batch_data) in enumerate(dataloader_steps):
            images = batch_data['images'].to(accelerator.device, dtype=weight_dtype)

            # 前向传播
            predictions = model(images)

            # 准备目标
            output_size = predictions[0].shape[2:]  # (H, W)
            targets = prepare_targets(batch_data, output_size)
            for key, value in targets.items():
                targets[key] = value.to(accelerator.device, dtype=weight_dtype)

            # 计算损失
            avg_factor = max(1, sum(len(centers) for centers in batch_data['cell_centers']))
            losses = criterion(predictions, targets, avg_factor=avg_factor)

            loss = losses['loss_heatmap']
            total_loss += loss.item() * images.shape[0]
            total_samples += images.shape[0]
            test_pbar.update(1)

    test_pbar.close()
    avg_loss = total_loss / max(total_samples, 1)

    # 记录验证指标
    accelerator.log({"val_loss": avg_loss}, step=global_step)

    # 输出最终的综合指标
    msg = (f"\nTotal Metrics\n"
           f"AvgLoss: {avg_loss:.6f}")
    logger.info(msg)
    file_logger.info(msg)

    if config.ema.enabled and ema_handler is not None:
        ema_handler.restore(model)
    model.train()

    return avg_loss


def walk_dataloaders(loaders: List[Tuple[str, torch.utils.data.DataLoader]]):
    """
    遍历多个数据加载器

    Args:
        loaders: 数据加载器列表

    Yields:
        (loader_name, batch_data)
    """
    if not loaders:
        return

    # 创建迭代器
    iterators = [(name, iter(loader)) for name, loader in loaders]

    while iterators:
        # 随机选择一个数据加载器
        idx = random.randint(0, len(iterators) - 1)
        name, iterator = iterators[idx]

        try:
            batch = next(iterator)
            yield name, batch
        except StopIteration:
            # 该数据加载器已耗尽，移除它
            iterators.pop(idx)


def main():
    """主训练函数"""
    global config
    if config is None:
        config = parse_args()

    # 准备训练环境
    accelerator, weight_dtype = prepare_training_enviornment_v2(config, logger)

    # 初始化模型、checkpoint复用等
    start_steps = 0
    ema_path = None
    model_state_dict = None
    optimizer_ckpt = None
    lr_scheduler_ckpt = None

    best_loss_model_record = os.path.join(config.basic.output_dir, "best_loss_model", "record.json")
    os.makedirs(Path(best_loss_model_record).parent, exist_ok=True)

    exists_checkpoints = list(Path(config.basic.output_dir).glob('checkpoint-*'))
    resume_from_checkpoint = config.checkpoint.resume.from_checkpoint
    if len(exists_checkpoints) > 0:
        resume_from_checkpoint = None

    if resume_from_checkpoint is not None and os.path.exists(resume_from_checkpoint):
        model_state_dict = torch.load(
            os.path.join(resume_from_checkpoint, "pytorch_model.bin"),
            map_location='cpu'
        )

        tmp_ema_path = os.path.join(resume_from_checkpoint, "pytorch_model_ema.bin")
        if config.ema.enabled and os.path.exists(tmp_ema_path):
            ema_path = tmp_ema_path

        load_state_dict_msg = f"load state dict from {resume_from_checkpoint}"
        if accelerator.is_main_process:
            msg = f"Reusing checkpoint from {resume_from_checkpoint}"
            logger.info(msg)
            file_logger.info(msg)

    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(config.basic.output_dir, f'checkpoint-{resume_steps}')
        model_state_dict = torch.load(
            os.path.join(resume_ckpt_dir, "pytorch_model.bin"),
            map_location='cpu'
        )

        tmp_ema_path = os.path.join(resume_ckpt_dir, "pytorch_model_ema.bin")
        if config.ema.enabled and os.path.exists(tmp_ema_path):
            ema_path = tmp_ema_path

        load_state_dict_msg = f"load state dict from {resume_ckpt_dir}"
        start_steps = resume_steps
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")
        if accelerator.is_main_process:
            msg = f"Reusing running checkpoint from {resume_ckpt_dir}"
            logger.info(msg)
            file_logger.info(msg)

    # 模型定义初始化, 解决分布式训练BatchNorm2D引起的梯度计算异常
    model = create_cycle_centernet_model(
        head_version="full",
        backbone_depth=config.model.backbone.depth,
        head_feat_channels=config.model.head.feat_channels,
        num_classes=config.model.task.num_classes,
    )
    # model = torch_utils.convert_batchnorm_to_apex_sync_batchnorm(model)
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)
    if model_state_dict is not None:
        model.load_state_dict(model_state_dict, strict=True)
        logger.info(f"model: {load_state_dict_msg}")
        file_logger.info(f"model: {load_state_dict_msg}")
    model.to(accelerator.device, weight_dtype)
    model.train()

    ema_handler = None
    if config.ema.enabled:
        ema_handler = EMAHandler(
            model=model,
            decay=config.ema.decay,
            device=accelerator.device,
            weight_dtype=weight_dtype,
            start_step=config.ema.start_step,
            update_period=config.ema.update_period,
            log_updates=False,
        )
        if ema_path is not None:
            ema_handler.load(ema_path)

    # 创建损失函数, 固定使用full版本
    loss_criterion = create_cycle_centernet_loss(
        version="full",
        heatmap_loss_weight=config.loss.weights.heatmap,
        offset_loss_weight=config.loss.weights.offset,
        center2vertex_loss_weight=config.loss.weights.center2vertex,
        vertex2center_loss_weight=config.loss.weights.vertex2center
    )
    loss_criterion = loss_criterion.to(accelerator.device, weight_dtype)

    # 初始化优化策略
    params_to_opt = model.parameters()

    optimizer = get_optimizer(
        config=config,
        params_to_opt=params_to_opt,
        optimizer_ckpt=optimizer_ckpt,
    )

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes
    assert config.training.batch_size % num_split == 0, \
        (f"batch_size: {config.training.batch_size} needs to be divisible by num_processes={num_split}")

    if config.basic.seed is not None:
        seed = int(config.basic.seed)
    else:
        seed = random.randint(1, 100000)

    # 准备数据集
    train_batch_size_per_device = config.training.batch_size // num_split
    train_datasets, train_loaders = prepare_dataloaders(
        config, "train", train_batch_size_per_device, seed=seed
    )
    _, val_loaders = prepare_dataloaders(
        config, "val", train_batch_size_per_device, seed=seed
    )

    if not train_loaders:
        raise ValueError("No training data loaders available!")

    train_dataloader_total_steps = sum(len(loader) for _, loader in train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps)
    max_train_steps = config.training.epochs * num_update_steps_per_epoch

    lr_scheduler = get_scheduler(
        config.training.scheduler.type,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        power=config.training.scheduler.power,
        num_cycles=config.training.scheduler.cosine.num_cycles,
        num_warmup_steps=config.training.scheduler.warmup.steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    # 最佳模型保存指标的初始化，基于验证损失
    best_loss_record_data = {"avg_loss": float('inf')}
    if os.path.exists(best_loss_model_record):
        with open(best_loss_model_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            best_loss_record_data["avg_loss"] = data["avg_loss"]

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)

    for i, loader in enumerate(train_loaders):
        flag, loader = loader
        loader = accelerator.prepare(loader)
        train_loaders[i] = (flag, loader)

    if accelerator.is_main_process:
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{config.distributed.tracker_project_name}-{exp_date}")

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = sum(len(loader) for _, loader in train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps)
    max_train_steps = config.training.epochs * num_update_steps_per_epoch

    if accelerator.is_main_process:
        msg = "\n"
        msg += "***** Running training *****\n"
        msg += f"  Num checkpoints to keep: {config.checkpoint.save.keep_num}\n"
        msg += f"  Enable EMA model: {config.ema.enabled}\n"
        msg += f"  Dataset seed: {seed}\n"
        msg += f"  best_loss_model_record_data: {best_loss_record_data}\n"
        msg += f"  Num examples = {sum(len(dataset) for dataset in train_datasets)}\n"
        msg += f"  Num epochs = {config.training.epochs}\n"
        msg += f"  Batch size per device = {train_batch_size_per_device}\n"
        msg += f"  Total train batch size (w. parallel, distributed & accumulation) = {config.training.batch_size}\n"
        msg += f"  Total optimization steps = {max_train_steps}\n"
        msg += "\n"
        logger.info(msg)
        file_logger.info(msg)

    first_epoch = 0
    global_step = start_steps
    exists_epoch_ckpts = list(Path(config.basic.output_dir).glob('model_epoch-*'))
    if len(exists_epoch_ckpts) > 0:
        epoch_numbers = [int(f.stem.split('-')[1]) for f in exists_epoch_ckpts]
        first_epoch = max(epoch_numbers) - 1

    # 初始化进度条
    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        disable=not accelerator.is_local_main_process,
    )

    # 训练循环
    for epoch in range(first_epoch, config.training.epochs):
        for step, (flag, batch) in enumerate(walk_dataloaders(train_loaders)):
            images = batch['images'].to(accelerator.device, dtype=weight_dtype)

            # 前向传播
            predictions = model(images)

            # 准备目标
            output_size = predictions[0].shape[2:]  # (H, W)
            targets = prepare_targets(batch, output_size)
            for key, value in targets.items():
                targets[key] = value.to(accelerator.device, dtype=weight_dtype)

            # 计算损失
            avg_factor = max(1, sum(len(centers) for centers in batch['cell_centers']))
            losses = loss_criterion(predictions, targets, avg_factor=avg_factor)

            loss = losses['loss_heatmap']

            # 反向传播
            accelerator.backward(loss)
            avg_loss = accelerator.gather(loss.repeat(config.training.batch_size)).mean().item()

            # 梯度裁剪
            if config.training.gradient.clip_norm:
                accelerator.clip_grad_norm_(params_to_opt, config.training.gradient.clip_value)

            # 优化器步骤
            optimizer.step()
            lr_scheduler.step()
            optimizer.zero_grad()

            if config.ema.enabled:
                ema_handler.update(accelerator.unwrap_model(model), global_step)

            # 更新进度条
            progress_bar.update(1)
            global_step += 1

            if accelerator.is_main_process:
                logs = {
                    "dflag": flag,
                    "loss": loss.detach().item(),
                    "avg_loss": avg_loss,
                    "lr": lr_scheduler.get_last_lr()[0],
                }
                progress_bar.set_postfix(**logs)
                accelerator.log(logs, step=global_step)

            if accelerator.is_main_process and global_step % config.checkpoint.save.steps == 0:
                save_path = os.path.join(config.basic.output_dir, f"checkpoint-{global_step}")
                save_state(
                    save_path,
                    model,
                    optimizer,
                    lr_scheduler,
                    accelerator,
                    config.checkpoint.save.keep_num,
                    ema_handler=ema_handler,
                )

                # 下面是根据不同记录保存最佳模型
                val_metrics = log_validation(
                    config, model, ema_handler, global_step, accelerator, weight_dtype, val_loaders
                )
                save_best_checkpoints(
                    config,
                    accelerator,
                    model,
                    ema_handler,
                    optimizer,
                    lr_scheduler,
                    global_step,
                    val_metrics,
                    best_loss_model_record,
                    best_loss_record_data,
                )

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process and config.checkpoint.save.every_n_epoch > 0 and (epoch + 1) % config.checkpoint.save.every_n_epoch == 0:
            save_path = os.path.join(config.basic.output_dir, f"model_epoch-{epoch + 1}")
            save_state(
                save_path,
                model,
                optimizer,
                lr_scheduler,
                accelerator,
                ema_handler=ema_handler,
            )

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(config.basic.output_dir, f"model_final")
        save_state(
            save_path,
            model,
            optimizer,
            lr_scheduler,
            accelerator,
            ema_handler=ema_handler,
        )
    accelerator.end_training()


if __name__ == '__main__':
    main()
